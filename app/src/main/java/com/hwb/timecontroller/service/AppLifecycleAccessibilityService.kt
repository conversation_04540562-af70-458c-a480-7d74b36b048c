package com.hwb.timecontroller.service

import android.accessibilityservice.AccessibilityService
import android.app.Service
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.accessibility.AccessibilityEvent
import android.widget.Toast
import com.hjq.toast.Toaster
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.hwb.timecontroller.activity.LoginActivity
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.business.AppDataCleanupManager
import com.hwb.timecontroller.business.AppLifecycleManager
import com.hwb.timecontroller.business.CountdownManager
import com.hwb.timecontroller.business.WhitelistManager
import com.hwb.timecontroller.service.keepalive.KeepAliveCapable
import com.hwb.timecontroller.service.keepalive.MutualKeepAliveManager
import com.hwb.timecontroller.utils.AccessibilityOverlayPermissionHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 应用生命周期监听无障碍服务
 * 监听应用启动和切换，对白名单和非白名单应用进行差异化处理
 * 支持互相保活机制
 */
class AppLifecycleAccessibilityService : AccessibilityService(), KeepAliveCapable {

    companion object {
        private const val BLOCK_DELAY_MS = 500L // 阻止非白名单应用的延迟时间

        // 服务实例，用于外部调用
        private var instance: AppLifecycleAccessibilityService? = null

        /**
         * 获取服务实例
         */
        fun getInstance(): AppLifecycleAccessibilityService? = instance
    }

    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    private val handler = Handler(Looper.getMainLooper())

    private var isDeviceLocked = false
    private var currentForegroundApp: String? = null
    private var previousForegroundApp: String? = null

    // 保活管理器
    private lateinit var keepAliveManager: MutualKeepAliveManager

    // 协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())
    private var countdownObserverJob: Job? = null

    // 用于跟踪设备锁定状态变化，减少日志输出
    private var lastDeviceLockState: Boolean? = null

    // 悬浮窗权限获取助手（精简版）
    private var overlayPermissionHelper: AccessibilityOverlayPermissionHelper? = null


    override fun onServiceConnected() {
        super.onServiceConnected()
        XLog.d("无障碍服务已连接")

        // 设置服务实例
        instance = this

        // 初始化保活管理器
        keepAliveManager = MutualKeepAliveManager(
            context = this,
            currentServiceClass = AppLifecycleAccessibilityService::class.java,
            serviceDisplayName = "无障碍服务"
        )

        // 注册需要保活的伙伴服务
        keepAliveManager.registerPartnerServices(
            CountdownService::class.java,
            FloatingWindowService::class.java,
            AppLifecycleManagerService::class.java,
            CheckService::class.java
        )

        // 启动保活
        keepAliveManager.startKeepAlive()

        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, AppDeviceAdminReceiver::class.java)

        // 初始化悬浮窗权限助手（精简版）
        overlayPermissionHelper = AccessibilityOverlayPermissionHelper(this)

        // 监听CountdownManager的状态变化
        observeCountdownState()

        // 自动获取悬浮窗权限并启动悬浮窗
        autoSetupFloatingWindow()
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        // 先让悬浮窗权限助手处理事件
        overlayPermissionHelper?.onAccessibilityEvent(event)

        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                handleWindowStateChanged(event)
            }

            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // 可以在这里处理窗口内容变化
                handleWindowContentChanged(event)
            }
        }
    }

    override fun onInterrupt() {
        XLog.d("无障碍服务被中断")
    }

    /**
     * 处理窗口状态变化事件
     */
    private fun handleWindowStateChanged(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return
        val className = event.className?.toString() ?: return

        XLog.d("窗口状态变化: $packageName - $className")

        // 检查应用启动拦截（在更新前台应用之前）
        if (shouldInterceptAppLaunch(packageName, className)) {
            interceptAppLaunch(packageName, className)
            return
        }

        // 更新当前前台应用
        XLog.d("检查应用切换: 当前前台应用=$currentForegroundApp, 新应用=$packageName")
        if (packageName != currentForegroundApp) {
            onAppSwitched(currentForegroundApp, packageName, className)
            // 记录前一个应用
            previousForegroundApp = currentForegroundApp
            currentForegroundApp = packageName
        } else {
            XLog.d("应用未切换，跳过处理")
        }

        // 注意：移除了原有的blockApp调用，现在使用新的拦截机制
    }

    /**
     * 处理窗口内容变化事件
     */
    private fun handleWindowContentChanged(event: AccessibilityEvent) {
        try {
            val packageName = event.packageName?.toString() ?: return

            // 如果设备已锁定且应用不在白名单中，进行额外检查
            if (isDeviceLocked && !WhitelistManager.isInWhitelist(packageName)) {
                XLog.d("设备锁定状态下检测到非白名单应用活动: $packageName")

                // 检查是否是系统关键应用
                if (!isSystemCriticalApp(packageName) && packageName != this.packageName) {
                    // 记录持续违规行为
                    recordPersistentViolation(packageName)

                    // 如果应用持续活动，可能需要更强的阻止措施
                    if (shouldApplyStrongerBlock(packageName)) {
                        XLog.w("应用 $packageName 持续违规，应用更强阻止措施")
                        blockApp(packageName, event.className?.toString() ?: "")
                    }
                }
            }

        } catch (e: Exception) {
            XLog.e("处理窗口内容变化事件时发生错误", e)
        }
    }

    /**
     * 记录持续违规行为
     */
    private fun recordPersistentViolation(packageName: String) {
        try {
            // 这里可以实现违规计数逻辑
            XLog.w("记录持续违规行为: $packageName")
        } catch (e: Exception) {
            XLog.e("记录持续违规行为失败", e)
        }
    }

    /**
     * 判断是否应该应用更强的阻止措施
     */
    private fun shouldApplyStrongerBlock(packageName: String): Boolean {
        // 这里可以实现基于违规频率的判断逻辑
        // 比如在短时间内多次违规则应用更强措施
        return false // 暂时返回false，避免过于激进
    }

    /**
     * 应用切换事件处理
     */
    private fun onAppSwitched(fromApp: String?, toApp: String, toClass: String) {
        try {
            XLog.d("应用切换: $fromApp -> $toApp($toClass)")

            val isToAppInWhitelist = WhitelistManager.isInWhitelist(toApp)
            val isFromAppInWhitelist = fromApp?.let { WhitelistManager.isInWhitelist(it) } ?: true

            if (isDeviceLocked) {
                if (isToAppInWhitelist) {
                    XLog.d("设备锁定状态下允许访问白名单应用: $toApp")
                } else {
                    XLog.w("设备锁定状态下尝试访问非白名单应用: $toApp")
                    // 记录违规尝试
                    recordViolationAttempt(toApp)
                }
            } else {
                XLog.d("设备未锁定，允许访问: $toApp")
            }

            // 记录应用使用统计
            recordAppUsage(fromApp, toApp)

            // 记录需要清理数据的应用
            AppDataCleanupManager.recordCleanupTargetApp(toApp)

            // 通知应用生命周期管理器
            AppLifecycleManager.onAppSwitched(toApp, toClass)

        } catch (e: Exception) {
            XLog.e("处理应用切换事件时发生错误", e)
        }
    }

    /**
     * 记录违规访问尝试
     */
    private fun recordViolationAttempt(packageName: String) {
        try {
            XLog.w("记录违规访问尝试: $packageName")
            // 这里可以添加违规记录逻辑，比如保存到数据库或发送统计信息
        } catch (e: Exception) {
            XLog.e("记录违规访问尝试失败", e)
        }
    }

    /**
     * 记录应用使用统计
     */
    private fun recordAppUsage(fromApp: String?, toApp: String) {
        try {
            // 这里可以添加应用使用统计逻辑
            XLog.d("应用使用统计: $fromApp -> $toApp")
        } catch (e: Exception) {
            XLog.e("记录应用使用统计失败", e)
        }
    }

    /**
     * 检查是否应该阻止应用
     */
    private fun shouldBlockApp(packageName: String): Boolean {
        try {
            // 如果设备未锁定，不阻止任何应用
            if (!isDeviceLocked) {
                return false
            }

            // 如果应用在白名单中，不阻止
            if (WhitelistManager.isInWhitelist(packageName)) {
                return false
            }

            // 检查是否是系统关键应用
            if (isSystemCriticalApp(packageName)) {
                XLog.d("跳过系统关键应用: $packageName")
                return false
            }

            // 检查是否是本应用
            if (packageName == this.packageName) {
                return false
            }

            XLog.d("需要阻止非白名单应用: $packageName")
            return true

        } catch (e: Exception) {
            XLog.e("检查应用是否需要阻止时发生错误", e)
            return false
        }
    }

    /**
     * 检查是否应该拦截应用启动
     */
    private fun shouldInterceptAppLaunch(packageName: String, className: String): Boolean {
        try {
            if (!AdminManager.isGovernanceState) {
                return false
            }

            if (WhitelistManager.isInUnconditionalWhitelist(packageName)) {
                return false
            }

            // 跳过CheckWhenLauncherActivity和LockActivity
            if (className.contains("CheckWhenLauncherActivity") ||
                className.contains("LockActivity")
            ) {
                return false
            }

            // 白名单应用不进行拦截
//            if (WhitelistManager.isAppInWhitelist(packageName)) {
//                return false
//            }

            XLog.d("检测到受控应用启动: $packageName")
            return true

        } catch (e: Exception) {
            XLog.e("检查应用启动拦截时发生错误", e)
            return false
        }
    }

    /**
     * 拦截应用启动
     * 注意：现在使用CheckService进行验证，不再启动CheckWhenLauncherActivity
     */
    private fun interceptAppLaunch(packageName: String, className: String) {
        try {
            XLog.d("拦截应用启动: $packageName - $className")

            // 记录需要清理数据的应用
            AppDataCleanupManager.recordCleanupTargetApp(packageName)

            // 通过CheckService进行应用启动权限验证
            // CheckService会根据验证结果决定是否启动LockActivity
            // 这里只需要记录拦截事件，具体验证逻辑由CheckService处理

            // 通知应用生命周期管理器
            AppLifecycleManager.onAppLaunchIntercepted(packageName, className)

            XLog.d("应用启动拦截完成，验证逻辑由CheckService处理")

        } catch (e: Exception) {
            XLog.e("拦截应用启动时发生错误", e)
        }
    }

    /**
     * 检查是否是系统关键应用
     */
    private fun isSystemCriticalApp(packageName: String): Boolean {
        val criticalApps = setOf(
            "com.android.systemui",
            "com.android.settings",
            "com.android.launcher",
            "com.android.launcher3",
            "android",
            "com.android.phone",
            "com.android.dialer",
            "com.android.emergency"
        )
        return criticalApps.contains(packageName)
    }

    /**
     * 阻止应用运行
     */
    private fun blockApp(packageName: String, className: String) {
        try {
            if (!devicePolicyManager.isDeviceOwnerApp(this.packageName)) {
                XLog.w("没有设备所有者权限，无法阻止应用")
                return
            }

            XLog.d("正在阻止应用: $packageName")

            // 延迟执行阻止操作，避免频繁触发
            handler.postDelayed({
                try {
                    // 方法1: 启动锁定Activity覆盖当前应用
                    val intent = Intent(this, LoginActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                                Intent.FLAG_ACTIVITY_CLEAR_TOP or
                                Intent.FLAG_ACTIVITY_SINGLE_TOP
                        putExtra("blocked_app", packageName)
                        putExtra("blocked_class", className)
                    }
                    startActivity(intent)

                    // 方法2: 如果需要，可以强制停止应用（谨慎使用）
                    // forceStopApp(packageName)

                    // 显示提示信息
                    showBlockedAppMessage(packageName)

                    // 通知应用生命周期管理器
                    AppLifecycleManager.onAppBlocked(packageName, "设备锁定状态下访问非白名单应用")

                    XLog.d("已阻止应用: $packageName")

                } catch (e: Exception) {
                    XLog.e("阻止应用时发生错误", e)
                }
            }, BLOCK_DELAY_MS)

        } catch (e: Exception) {
            XLog.e("阻止应用操作失败", e)
        }
    }


    /**
     * 强制停止应用（需要设备管理权限）
     */
    private fun forceStopApp(packageName: String) {
        try {
            if (devicePolicyManager.isDeviceOwnerApp(this.packageName)) {
                // 使用设备管理权限强制停止应用
                val intent = Intent()
                intent.component = ComponentName(
                    "com.android.settings",
                    "com.android.settings.applications.InstalledAppDetails"
                )
                intent.putExtra("com.android.settings.ApplicationPkgName", packageName)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
            }
        } catch (e: Exception) {
            XLog.e("强制停止应用失败: $packageName", e)
        }
    }

    /**
     * 显示应用被阻止的消息
     */
    private fun showBlockedAppMessage(packageName: String) {
        handler.post {
            Toast.makeText(
                this,
                "应用 $packageName 不在白名单中，已被阻止",
                Toast.LENGTH_SHORT
            ).show()
        }
    }


    /**
     * 设置设备锁定状态
     */
    fun setDeviceLocked(locked: Boolean) {
        if (isDeviceLocked != locked) {
            XLog.d("设备锁定状态更新: $locked")
        }
        isDeviceLocked = locked
    }

    /**
     * 监听CountdownManager的状态变化
     */
    private fun observeCountdownState() {
        countdownObserverJob = serviceScope.launch {
            CountdownManager.countdownData.collect { data ->
                // 只在设备锁定状态发生变化时输出日志，减少日志量
                if (lastDeviceLockState != data.isDeviceLocked) {
                    XLog.d("倒计时数据变化: 剩余时间: ${data.remainingTimeMillis}, 设备锁定状态: ${data.isDeviceLocked}")
                    lastDeviceLockState = data.isDeviceLocked
                }

                // 更新设备锁定状态
                setDeviceLocked(data.isDeviceLocked)
            }
        }
    }

    /**
     * 自动设置悬浮窗
     */
    private fun autoSetupFloatingWindow() {
        try {
            // 检查是否有设备所有者权限
            if (!devicePolicyManager.isDeviceOwnerApp(packageName)) {
                XLog.w("没有设备所有者权限，跳过悬浮窗设置，返回主页面")
                Toaster.show("没有设备所有者权限，请联系管理员")
                return
            }

            // 检查是否已经有悬浮窗权限
            if (hasOverlayPermission()) {
                XLog.d("已有悬浮窗权限，启动悬浮窗服务并返回主页面")
                try {
                    FloatingWindowService.start(this@AppLifecycleAccessibilityService)
                    Toast.makeText(
                        this@AppLifecycleAccessibilityService,
                        "悬浮窗已启动",
                        Toast.LENGTH_SHORT
                    ).show()
                } catch (e: Exception) {
                    XLog.e("启动悬浮窗服务失败", e)
                }
                return
            }

            overlayPermissionHelper?.autoGrantOverlayPermission(
                onGranted = {
                    XLog.d("悬浮窗权限获取成功，启动悬浮窗服务")
                    try {
                        FloatingWindowService.start(this@AppLifecycleAccessibilityService)
                        Toast.makeText(
                            this@AppLifecycleAccessibilityService,
                            "悬浮窗已启动",
                            Toast.LENGTH_SHORT
                        ).show()
                        // 悬浮窗启动成功后返回主页面
                        Toaster.show("自动获取悬浮窗权限成功，请返回主页")
                    } catch (e: Exception) {
                        XLog.e("启动悬浮窗服务失败", e)
                        Toaster.show("获取悬浮窗权限失败，请手动授予")
                    }
                },
                onFailed = {
                    XLog.w("悬浮窗权限获取失败，返回主页面")
                    Toast.makeText(
                        this@AppLifecycleAccessibilityService,
                        "悬浮窗权限获取失败",
                        Toast.LENGTH_SHORT
                    ).show()

                    Toaster.show("获取悬浮窗权限失败，请手动授予")
                }
            )
        } catch (e: Exception) {
            XLog.e("自动设置悬浮窗失败", e)

            Toaster.show("获取悬浮窗权限失败，请手动授予")
        }
    }

    /**
     * 检查是否有悬浮窗权限
     */
    private fun hasOverlayPermission(): Boolean {
        return try {
            Settings.canDrawOverlays(this)
        } catch (e: Exception) {
            XLog.e("检查悬浮窗权限失败", e)
            false
        }
    }


    /**
     * 检查是否应该返回MainActivity
     */
    private fun shouldReturnToMainActivity(): Boolean {
        return try {
            // 检查当前权限状态，决定是否需要返回MainActivity
            val hasDeviceOwner = devicePolicyManager.isDeviceOwnerApp(packageName)
            val hasAccessibility = true // 无障碍服务已启用（当前服务正在运行）
            val hasOverlay = hasOverlayPermission()

            // 根据用户需求，即使有完整权限也返回MainActivity
            if (hasDeviceOwner && hasAccessibility && hasOverlay) {
                XLog.d("已有完整权限，但根据配置仍需要返回MainActivity")
                return true
            }

            // 其他情况也需要返回MainActivity让用户处理
            true
        } catch (e: Exception) {
            XLog.e("检查是否应该返回MainActivity失败，默认返回", e)
            true
        }
    }

    override fun onDestroy() {
        try {
            XLog.d("无障碍服务开始销毁")

            // 取消协程作用域
            countdownObserverJob?.cancel()
            serviceScope.cancel()

            // 清理悬浮窗权限助手
            overlayPermissionHelper?.cleanup()
            overlayPermissionHelper = null

            // 停止保活
            if (::keepAliveManager.isInitialized) {
                keepAliveManager.onServiceDestroyed()
            }

            // 清除服务实例
            instance = null

            XLog.d("无障碍服务销毁完成")

        } catch (e: Exception) {
            XLog.e("无障碍服务销毁失败", e)
        } finally {
            super.onDestroy()
        }
    }

    // 实现KeepAliveCapable接口
    override fun getKeepAliveManager(): MutualKeepAliveManager {
        return keepAliveManager
    }

    override fun onPartnerServiceDied(serviceClass: Class<out Service>) {
        super.onPartnerServiceDied(serviceClass)
        XLog.w("无障碍服务检测到伙伴服务死亡: ${serviceClass.simpleName}")
    }


    /**
     * 判断是否为系统应用
     */
    private fun isSystemApp(packageName: String): Boolean {
        val systemApps = setOf(
            "com.android.launcher",
            "com.android.launcher3",
            "com.android.systemui",
            "android",
            "com.google.android.apps.nexuslauncher",
            "com.miui.home", // MIUI 桌面
            "com.huawei.android.launcher", // 华为桌面
            "com.oppo.launcher", // OPPO 桌面
            "com.vivo.launcher" // vivo 桌面
        )
        return systemApps.contains(packageName)
    }


    override fun onPartnerServiceRestarted(serviceClass: Class<out Service>) {
        super.onPartnerServiceRestarted(serviceClass)
        XLog.i("无障碍服务检测到伙伴服务重启: ${serviceClass.simpleName}")
    }


}
