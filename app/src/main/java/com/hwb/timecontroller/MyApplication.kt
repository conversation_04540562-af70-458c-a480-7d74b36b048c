package com.hwb.timecontroller

import android.Manifest
import android.app.Activity
import android.app.Application
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Environment
import android.util.Log
import com.elvishew.xlog.LogConfiguration
import com.elvishew.xlog.LogLevel
import com.elvishew.xlog.XLog
import com.elvishew.xlog.flattener.PatternFlattener
import com.elvishew.xlog.printer.AndroidPrinter
import com.elvishew.xlog.printer.Printer
import com.elvishew.xlog.printer.file.FilePrinter
import com.elvishew.xlog.printer.file.backup.NeverBackupStrategy
import com.elvishew.xlog.printer.file.clean.FileLastModifiedCleanStrategy
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator
import com.hjq.toast.Toaster
import com.hwb.timecontroller.business.AppDataCleanupManager
import com.hwb.timecontroller.business.AppLifecycleManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.network.GlobalWebSocketManager
import com.hwb.timecontroller.network.NetworkManager
import com.hwb.timecontroller.network.OkHttpUpdateHttpService
import com.hwb.timecontroller.util.UpdateUtils
import com.hwb.timecontroller.utils.AutoRecoveryManager
import com.hwb.timecontroller.utils.SystemMonitor
import com.kongzue.dialogx.DialogX
import com.tencent.mmkv.MMKV
import com.xuexiang.xupdate.XUpdate
import com.xuexiang.xupdate.entity.UpdateError
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


/**
 * <p>
 * Author:huangwubin
 * <p>
 * changeLogs:
 * 2025/6/30: First created this class.
 */
class MyApplication : Application() {

    companion object {
        private const val TAG = "MyApplication"
        lateinit var myApp: MyApplication
    }

    // 系统监控和自动恢复管理器
    private var systemMonitor: SystemMonitor? = null
    private var autoRecoveryManager: AutoRecoveryManager? = null

    //日志的文件存储是否已经初始化了
    private var isInitLogFileSystem = false

    // Activity生命周期回调
    private val activityLifecycleCallbacks = object : ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            XLog.d("Activity创建: ${activity.javaClass.simpleName}")
        }

        override fun onActivityStarted(activity: Activity) {
            XLog.d("Activity启动: ${activity.javaClass.simpleName}")
        }

        override fun onActivityResumed(activity: Activity) {
            val className = activity.javaClass.name
            XLog.d("Activity恢复: ${activity.javaClass.simpleName}")

            // 通知AppLifecycleManager自己应用的Activity变为可见
            AppLifecycleManager.onOwnActivityResumed(className)
        }

        override fun onActivityPaused(activity: Activity) {
            val className = activity.javaClass.name
            XLog.d("Activity暂停: ${activity.javaClass.simpleName}")

            // 通知AppLifecycleManager自己应用的Activity变为不可见
            AppLifecycleManager.onOwnActivityPaused(className)
        }

        override fun onActivityStopped(activity: Activity) {
            XLog.d("Activity停止: ${activity.javaClass.simpleName}")
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            // 不需要处理
        }

        override fun onActivityDestroyed(activity: Activity) {
            XLog.d("Activity销毁: ${activity.javaClass.simpleName}")
        }
    }

    override fun onCreate() {
        super.onCreate()
        myApp = this
        Toaster.init(this)
        DialogX.init(this);
        initMMKV()
        initLog()

        // 注册Activity生命周期回调
        registerActivityLifecycleCallbacks(activityLifecycleCallbacks)

        // 初始化用户管理器
        initUserManager()

        // 初始化应用数据清理管理器
        initAppDataCleanupManager()

        // 初始化系统监控
        initSystemMonitoring()

        // 初始化XUpdate
        initXUpdate()

        // 初始化全局WebSocket连接 - 暂时注释，后续改为HTTP长轮询
        // initGlobalWebSocket()
        XLog.w("-----注意，重启了应用！！！-----")
    }

    fun initLog() {
        //已经初始化过了
        if (isInitLogFileSystem) {
            return
        }
        Log.d(TAG, "初始化日志系统")

        try {
            val isDebug = BuildConfig.DEBUG
            val config = LogConfiguration.Builder()
                .logLevel(
                    if (isDebug)
                        LogLevel.ALL // 指定日志级别，低于该级别的日志将不会被打印，默认为 LogLevel.ALL
                    else
                        LogLevel.INFO // Release版本显示INFO级别，避免丢失重要日志
                )
//            .tag("MY_TAG") // 指定 TAG，默认为 "X-LOG"
//            .enableThreadInfo() // 允许打印线程信息，默认禁止
//            .enableStackTrace(2) // 允许打印深度为 2 的调用栈信息，默认禁止
//            .enableBorder() // 允许打印日志边框，默认禁止
//            .jsonFormatter(MyJsonFormatter()) // 指定 JSON 格式化器，默认为 DefaultJsonFormatter
//            .xmlFormatter(MyXmlFormatter()) // 指定 XML 格式化器，默认为 DefaultXmlFormatter
//            .throwableFormatter(MyThrowableFormatter()) // 指定可抛出异常格式化器，默认为 DefaultThrowableFormatter
//            .threadFormatter(MyThreadFormatter()) // 指定线程信息格式化器，默认为 DefaultThreadFormatter
//            .stackTraceFormatter(MyStackTraceFormatter()) // 指定调用栈信息格式化器，默认为 DefaultStackTraceFormatter
//            .borderFormatter(MyBoardFormatter()) // 指定边框格式化器，默认为 DefaultBorderFormatter
//            .addObjectFormatter<AnyClass?>(
//                AnyClass::class.java,  // 为指定类型添加对象格式化器
//                AnyClassObjectFormatter()
//            ) // 默认使用 Object.toString()
//            .addInterceptor(
//                BlacklistTagsFilterInterceptor( // 添加黑名单 TAG 过滤器
//                    "blacklist1", "blacklist2", "blacklist3"
//                )
//            )
//            .addInterceptor(MyInterceptor()) // 添加一个日志拦截器
                .build()

            val androidPrinter: Printer = AndroidPrinter(true) // 通过 android.util.Log 打印日志的打印器

            // 尝试初始化文件日志系统
            val filePrinter = initFileLogSystem(isDebug)

            if (filePrinter != null) {
                XLog.init( // 初始化 XLog
                    config,  // 指定日志配置，如果不指定，会默认使用 new LogConfiguration.Builder().build()
                    androidPrinter,  // 添加任意多的打印器。如果没有添加任何打印器，会默认使用 AndroidPrinter(Android)/ConsolePrinter(java)
                    filePrinter
                )
            } else {
                XLog.init(config, androidPrinter)
            }

        } catch (e: Exception) {
            Log.e(TAG, "初始化日志系统失败", e)
            // 确保至少有基本的日志功能
            try {
                val config = LogConfiguration.Builder()
                    .logLevel(LogLevel.ALL)
                    .build()
                val androidPrinter: Printer = AndroidPrinter(true)
                XLog.init(config, androidPrinter)
            } catch (fallbackException: Exception) {
                Log.e(TAG, "初始化基础日志系统也失败", fallbackException)
            }
        }
    }

    /**
     * 初始化文件日志系统
     * @param isDebug 是否为调试模式
     * @return FilePrinter实例，如果初始化失败则返回null
     */
    private fun initFileLogSystem(isDebug: Boolean): Printer? {
        try {
            // 优先尝试使用Documents目录（用户方便找到日志）
            val documentsDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            val logDir = if (canWriteToDirectory(documentsDir)) {
                Log.d(TAG, "使用Documents目录存储日志: ${documentsDir.absolutePath}")
                isInitLogFileSystem = true
                documentsDir.absolutePath
            } else {
                // 如果无法写入Documents目录，尝试使用Device Owner权限
                if (tryGrantStoragePermissionWithDeviceOwner()) {
                    Log.d(TAG, "通过Device Owner获取存储权限成功，使用Documents目录")
                    isInitLogFileSystem = true
                    documentsDir.absolutePath
                } else {
                    // 最后使用应用私有目录
                    val privateDir = getExternalFilesDir("logs")
                    Log.d(TAG, "使用应用私有目录存储日志: ${privateDir?.absolutePath}")
                    privateDir?.absolutePath
                }
            }

            if (logDir != null) {
                val saveLogDays = if (isDebug) 7 else 30

                return FilePrinter.Builder(logDir + "/${getString(R.string.app_name)}_logs")
                    .fileNameGenerator(DateFileNameGenerator()) // 指定日志文件名生成器
                    .backupStrategy(NeverBackupStrategy()) // 指定日志文件备份策略
                    .cleanStrategy(FileLastModifiedCleanStrategy(saveLogDays * 24L * 60L * 60L * 1000L)) // 指定日志文件清除策略
                    .flattener(PatternFlattener("{d yyyy/MM/dd HH:mm:ss} {l}|{t}: {m}"))
                    .build()
            }

        } catch (e: Exception) {
            Log.e(TAG, "初始化文件日志系统失败", e)
        }

        return null
    }

    /**
     * 检查是否可以写入指定目录
     */
    private fun canWriteToDirectory(directory: java.io.File?): Boolean {
        return try {
            if (directory == null || !directory.exists()) {
                false
            } else {
                // 在Android 10+中，检查是否可以写入Documents目录
                // 对于Android 10+，直接检查权限
                checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查目录写入权限失败", e)
            false
        }
    }

    /**
     * 尝试通过Device Owner权限获取存储权限
     */
    private fun tryGrantStoragePermissionWithDeviceOwner(): Boolean {
        return try {
            val devicePolicyManager =
                getSystemService(DEVICE_POLICY_SERVICE) as? DevicePolicyManager
            val adminComponent = ComponentName(this, AppDeviceAdminReceiver::class.java)

            if (devicePolicyManager?.isDeviceOwnerApp(packageName) == true) {
                Log.d(TAG, "当前应用是Device Owner，尝试获取存储权限")

                // Device Owner可以静默授予权限
                try {
                    // 检查是否已有权限
                    devicePolicyManager.setPermissionGrantState(
                        adminComponent,
                        packageName,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        DevicePolicyManager.PERMISSION_GRANT_STATE_GRANTED
                    )
                    val hasPermission =
                        checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                    Log.d(TAG, "存储权限检查结果: $hasPermission")
                    return hasPermission
                } catch (e: Exception) {
                    Log.e(TAG, "检查存储权限失败", e)
                }

                // 对于Device Owner，通常已经有足够的权限访问存储
                return true
            } else {
                Log.d(TAG, "当前应用不是Device Owner，无法通过Device Owner获取权限")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "通过Device Owner获取存储权限失败", e)
            false
        }
    }

    /**
     * 初始化系统监控
     */
    private fun initSystemMonitoring() {
        try {
            XLog.d("初始化系统监控")

            // 创建系统监控器
            systemMonitor = SystemMonitor(this)

            // 创建自动恢复管理器
            systemMonitor?.let { monitor ->
                autoRecoveryManager = AutoRecoveryManager(this, monitor)
            }

            // 启动监控
            systemMonitor?.startMonitoring()
            autoRecoveryManager?.startAutoRecovery()

            XLog.d("系统监控初始化完成")

        } catch (e: Exception) {
            XLog.e("初始化系统监控失败", e)
        }
    }

    private fun initMMKV() {
        val rootDir: String = MMKV.initialize(this)
        println("mmkv root: $rootDir")
    }

    /**
     * 初始化用户管理器
     */
    private fun initUserManager() {
        try {
            XLog.d("初始化用户管理器")
            UserManager.initialize(this)
            XLog.d("用户管理器初始化完成")
        } catch (e: Exception) {
            XLog.e("初始化用户管理器失败", e)
        }
    }

    /**
     * 初始化应用数据清理管理器
     */
    private fun initAppDataCleanupManager() {
        try {
            XLog.d("初始化应用数据清理管理器")
            AppDataCleanupManager.initialize()
            XLog.d("应用数据清理管理器初始化完成")
        } catch (e: Exception) {
            XLog.e("初始化应用数据清理管理器失败", e)
        }
    }


    override fun onTerminate() {
        super.onTerminate()

        try {
            XLog.d("应用终止，清理系统监控资源")

            // 停止系统监控
            systemMonitor?.stopMonitoring()
            autoRecoveryManager?.stopAutoRecovery()

            // 清理引用
            systemMonitor = null
            autoRecoveryManager = null

        } catch (e: Exception) {
            XLog.e("清理系统监控资源失败", e)
        }
    }

    /**
     * 获取系统监控器实例
     */
    fun getSystemMonitor(): SystemMonitor? {
        return systemMonitor
    }

    /**
     * 获取自动恢复管理器实例
     */
    fun getAutoRecoveryManager(): AutoRecoveryManager? {
        return autoRecoveryManager
    }

    /**
     * 初始化XUpdate版本更新框架
     */
    private fun initXUpdate() {
        try {
            XLog.d("初始化XUpdate版本更新框架")

            // 创建自定义HTTP服务
            val updateHttpService = OkHttpUpdateHttpService(NetworkManager.httpClient)

            XUpdate.get()
                .debug(BuildConfig.DEBUG)
                .isWifiOnly(false)  // 允许在移动网络下检查更新
                .isGet(true)  // 使用GET请求检查版本
                .isAutoMode(false)  // 非自动模式
                .param("versionCode", UpdateUtils.getVersionCode(this))  // 设置默认公共请求参数
                .param("appKey", packageName)
                .supportSilentInstall(true)  // 支持静默安装
                .setIUpdateHttpService(updateHttpService)  // 设置自定义HTTP服务
                .setOnUpdateFailureListener { error ->
                    when (error.code) {
                        UpdateError.ERROR.CHECK_NO_NEW_VERSION -> {
                            XLog.d("检查更新：当前已是最新版本")
                        }

                        UpdateError.ERROR.CHECK_JSON_EMPTY -> {
                            XLog.e("更新失败：下载失败", "检查更新：服务器返回数据为空")
                        }

                        UpdateError.ERROR.DOWNLOAD_FAILED -> {
                            XLog.e("初始化XUpdate失败")
                        }

                        else -> {
                            XLog.e("更新失败：${error.message}")
                        }
                    }
                }
                .init(this)

            XLog.d("XUpdate初始化完成")
        } catch (e: Exception) {
            XLog.e(e)
        }
    }

    /**
     * 初始化全局WebSocket连接 - 增强异常处理
     */
    private fun initGlobalWebSocket() {
        try {
            XLog.d("初始化全局WebSocket连接")

            // 在后台线程中连接WebSocket，使用更安全的异常处理
            GlobalScope.launch {
                try {
                    GlobalWebSocketManager.connect()
                    XLog.d("全局WebSocket连接初始化完成")
                } catch (e: Exception) {
                    // WebSocket连接失败不应该影响应用的其他功能
                    when (e) {
                        is java.net.UnknownHostException -> {
                            XLog.e(
                                "WebSocket连接初始化失败，将在需要时重试连接",
                                "WebSocket服务器地址无法解析，将在需要时重试连接"
                            )
                        }

                        is java.net.ConnectException -> {
                            XLog.w("WebSocket服务器连接失败，将在需要时重试连接")
                        }

                        else -> {
                            XLog.w(e)
                        }
                    }
                }
            }

        } catch (e: Exception) {
            // 确保WebSocket初始化失败不会影响应用启动
            XLog.e("WebSocket初始化过程发生异常，应用将继续正常运行", e)
        }
    }

}