package com.hwb.timecontroller.constant

/**
 * 应用常量定义
 * 统一管理项目中的常量，避免重复定义
 * 
 * Author: huangwubin
 * Date: 2025/7/26
 */
object AppConstants {

    /**
     * 常用应用名称列表
     * 用于无障碍服务检测应用列表页面
     */
    val COMMON_APP_NAMES = listOf(
        "相机", "Camera", "图库", "Gallery", "Photos",
        "微信", "WeChat", "QQ", "支付宝", "Alipay", "淘宝", "Taobao",
        "抖音", "TikTok", "快手", "Kuaishou", "微博", "Weibo",
        "Chrome", "Firefox", "Safari", "浏览器", "Browser",
        "音乐", "Music", "视频", "Video", "播放器", "Player",
        "地图", "Maps", "导航", "Navigation", "滴滴", "Didi",
        "美团", "<PERSON><PERSON><PERSON>", "饿了么", "Eleme", "京东", "JD",
        "Time Controller"
    )

    /**
     * 系统应用包名列表
     * 用于判断是否为系统关键应用
     */
    val SYSTEM_APP_PACKAGES = setOf(
        "com.android.launcher",
        "com.android.launcher3",
        "com.android.systemui",
        "android",
        "com.google.android.apps.nexuslauncher",
        "com.miui.home", // MIUI 桌面
        "com.huawei.android.launcher", // 华为桌面
        "com.oppo.launcher", // OPPO 桌面
        "com.vivo.launcher" // vivo 桌面
    )

    /**
     * 输入法应用包名列表
     */
    val INPUT_METHOD_PACKAGES = setOf(
        "com.google.android.inputmethod.latin", // Google 输入法
        "com.android.inputmethod.latin" // 模拟器输入法
    )

    /**
     * 应用列表页面标题关键词
     * 用于检测是否为应用管理页面
     */
    val APP_LIST_TITLE_KEYWORDS = listOf(
        "应用列表", "应用管理", "应用权限", "App list", "App permissions",
        "管理应用", "Manage apps", "应用信息", "App info",
        "悬浮窗权限", "Overlay permission", "显示在其他应用上层"
    )

    /**
     * RecyclerView/ListView 类名列表
     * 用于检测列表控件
     */
    val LIST_VIEW_CLASS_NAMES = listOf(
        "RecyclerView", "ListView", "list"
    )

    /**
     * 网络相关常量
     */
    object Network {
        const val REQUEST_TIMEOUT_MS = 30000L
        const val CONNECT_TIMEOUT_MS = 15000L
        const val SOCKET_TIMEOUT_MS = 30000L
        
        // WebSocket 配置
        const val WS_PING_INTERVAL_MS = 30000L
        const val WS_MAX_RECONNECT_ATTEMPTS = 5
        const val WS_RECONNECT_DELAY_MS = 5000L
    }

    /**
     * 服务保活相关常量
     */
    object KeepAlive {
        const val HEARTBEAT_INTERVAL_MS = 30000L // 心跳间隔30秒
        const val MONITOR_INTERVAL_MS = 45000L   // 监控间隔45秒
        const val SERVICE_START_DELAY_MS = 2000L // 服务启动延迟2秒
        const val MAX_RESTART_ATTEMPTS = 3       // 最大重启尝试次数
        const val MAX_HEARTBEAT_OFFSET_MS = 10000L // 最大心跳偏移10秒
        const val HEARTBEAT_TIMEOUT_MS = 90000L // 心跳超时90秒
    }

    /**
     * 存储相关常量
     */
    object Storage {
        const val KEY_CLEANUP_TARGET_APPS = "cleanup_target_apps"
        const val KEY_WHITELIST = "customer_whitelist_packages"
        const val KEY_LAST_CHECK_TIME = "update_last_check_time"
        const val KEY_IGNORED_VERSION = "update_ignored_version"
        const val KEY_AUTO_CHECK_ENABLED = "update_auto_check_enabled"
        const val KEY_WIFI_ONLY = "update_wifi_only"
        const val KEY_UPDATE_HISTORY = "update_history"
    }

    /**
     * 时间相关常量
     */
    object Time {
        const val MINUTE_IN_MILLIS = 60 * 1000L
        const val SECOND_IN_MILLIS = 1000L
        const val RECOVERY_CHECK_INTERVAL_MS = 30000L // 恢复检查间隔30秒
        const val RECOVERY_COOLDOWN_MS = 60000L // 恢复冷却时间60秒
        const val MONITOR_INTERVAL_MS = 15000L // 监控间隔15秒
        const val PERFORMANCE_MONITOR_INTERVAL_MS = 30000L // 性能监控间隔30秒
    }

    /**
     * UI 相关常量
     */
    object UI {
        const val CLICK_TIME_WINDOW = 3000L // 3秒时间窗口
        const val REQUIRED_CLICKS = 5 // 需要5次点击
        const val ANIMATION_DURATION = 1000L // 动画持续时间1秒
        const val DELAY_BEFORE_RESTART = 1000L // 重启前延迟1秒
    }

    /**
     * 权限相关常量
     */
    object Permissions {
        const val ACCESSIBILITY_SERVICE_SETTLE_TIME_MS = 5000L // 无障碍服务稳定时间
        const val PERMISSION_RECHECK_DELAY_MS = 3000L // 权限重新检查延迟
    }
}
