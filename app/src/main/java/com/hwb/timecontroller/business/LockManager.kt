package com.hwb.timecontroller.business

import android.content.Context
import android.content.Intent
import com.hwb.timecontroller.activity.LoginActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 锁定管理器
 * 使用StateFlow管理锁定状态，通过定期检测确保锁定页面始终在前台
 * 替代startLockTask机制，避免系统级锁定风险
 * 
 * Author: huangwubin
 * Date: 2025/7/4
 */
object LockManager {

    /**
     * 锁定状态枚举
     */
    enum class LockState {
        NONE,                    // 无锁定
        LOCK_ACTIVITY,          // 需要显示LockActivity
    }

    /**
     * 锁定数据类
     */
    data class LockData(
        val state: LockState = LockState.NONE,
        val targetPackageName: String? = null,
        val targetClassName: String? = null,
        val targetAppName: String? = null,
        val previousPackageName: String? = null,
        val reason: String? = null,
        val timestamp: Long = System.currentTimeMillis()
    )

    // 私有的可变StateFlow
    private val _lockData = MutableStateFlow(LockData())

    // 公开的只读StateFlow
    val lockData: StateFlow<LockData> = _lockData.asStateFlow()

    // 协程作用域和检测任务
    private val lockScope = CoroutineScope(Dispatchers.Main + Job())
    private var periodicCheckJob: Job? = null

    // 检测间隔（毫秒）
    private const val CHECK_INTERVAL_MS = 3000L

    // 应用上下文（用于启动Activity）
    private var applicationContext: Context? = null

    /**
     * 启动锁定Activity
     */
    private fun launchLockActivity(lockData: LockData) {
        try {
            val context = applicationContext ?: run {
                XLog.e("应用上下文为空，无法启动锁定Activity")
                return
            }

            val intent = when (lockData.state) {
                LockState.LOCK_ACTIVITY -> {
                    Intent(context, LoginActivity::class.java).apply {
                        putExtra("blocked_app", lockData.targetPackageName)
                        putExtra("blocked_class", lockData.targetClassName)
                        putExtra("lock_reason", lockData.reason)
                    }
                }
                else -> {
                    XLog.w("未知的锁定状态: ${lockData.state}")
                    return
                }
            }

            // 移除FLAG_ACTIVITY_CLEAR_TOP避免影响EmptyActivity的startLockTask状态
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP

            context.startActivity(intent)
            XLog.d("启动锁定Activity: ${lockData.state}")

        } catch (e: Exception) {
            XLog.e("启动锁定Activity失败", e)
        }
    }

    /**
     * 开始定期检测
     */
    private fun startPeriodicCheck() {
        // 如果已经在检测，先停止
        stopPeriodicCheck()

        periodicCheckJob = lockScope.launch {
            XLog.d("开始定期检测锁定状态")
            
            while (_lockData.value.state != LockState.NONE) {
                try {
                    delay(CHECK_INTERVAL_MS)
                    
                    val currentLockData = _lockData.value
                    if (currentLockData.state == LockState.NONE) {
                        break
                    }

                    // 检查当前前台应用
                    val currentForegroundApp = AppLifecycleManager.getCurrentForegroundApp()
                    val currentForegroundClass = AppLifecycleManager.getCurrentForegroundClass()
                    
                    // 判断是否需要强制启动锁定页面
                    if (shouldForceLaunchLockActivity(currentForegroundApp, currentForegroundClass, currentLockData)) {
                        XLog.d("检测到锁定页面不在前台，强制启动: 当前前台=$currentForegroundApp")
                        launchLockActivity(currentLockData)
                    }

                } catch (e: Exception) {
                    XLog.e("定期检测过程中发生错误", e)
                }
            }
            
            XLog.d("定期检测结束")
        }
    }

    /**
     * 停止定期检测
     */
    private fun stopPeriodicCheck() {
        periodicCheckJob?.cancel()
        periodicCheckJob = null
        XLog.d("停止定期检测")
    }

    /**
     * 判断是否需要强制启动锁定页面
     */
    private fun shouldForceLaunchLockActivity(
        currentForegroundApp: String?,
        currentForegroundClass: String?,
        lockData: LockData
    ): Boolean {
        if (currentForegroundApp == null) {
            return false
        }

        val ownPackageName = applicationContext?.packageName ?: return false

        // 如果当前前台应用是自己的应用，检查是否是锁定相关的Activity
        if (currentForegroundApp == ownPackageName) {
            val expectedActivityClass = when (lockData.state) {
                LockState.LOCK_ACTIVITY -> "LockActivity"
                else -> return false
            }
            
            // 如果当前Activity不是期望的锁定Activity，需要强制启动
            return !(currentForegroundClass?.contains(expectedActivityClass, ignoreCase = true) ?: true)
        }

        // 如果当前前台应用不是自己的应用，需要强制启动锁定页面
        return true
    }

    /**
     * 销毁LockManager
     */
    fun destroy() {
        try {
            XLog.d("销毁LockManager")
            stopPeriodicCheck()
            lockScope.cancel()
            applicationContext = null
        } catch (e: Exception) {
            XLog.e("销毁LockManager失败", e)
        }
    }
}
