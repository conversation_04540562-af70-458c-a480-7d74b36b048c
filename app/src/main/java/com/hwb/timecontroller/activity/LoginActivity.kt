package com.hwb.timecontroller.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.util.Base64
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import coil.load
import com.elvishew.xlog.XLog
import com.hi.dhl.binding.viewbind
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.databinding.ActivityLockBinding
import com.hwb.timecontroller.viewModel.LoginViewModel

class LockActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_TARGET_PACKAGE_NAME = "target_package_name"
        const val EXTRA_TARGET_CLASS_NAME = "target_class_name"
        const val EXTRA_TARGET_APP_NAME = "target_app_name"
        const val EXTRA_PREVIOUS_PACKAGE_NAME = "previous_package_name"
        const val EXTRA_SHOW_LOGIN_UI = "show_login_ui"
        const val EXTRA_ENTRY_TYPE = "entry_type"
    }

    /**
     * LockActivity入口类型枚举
     */
    enum class LockActivityEntryType {
        THIRD_PARTY_APP,    // 第三方应用调用
        COUNTDOWN_ENDED     // 倒计时结束
    }

    // 目标应用信息
    private var targetPackageName: String? = null
    private var targetClassName: String? = null
    private var targetAppName: String? = null
    private var previousPackageName: String? = null
    private var showLoginUI: Boolean = false
    private var entryType: LockActivityEntryType = LockActivityEntryType.THIRD_PARTY_APP

    // ViewModel
    private val loginViewModel by viewModels<LoginViewModel>()

    private val binding: ActivityLockBinding by viewbind()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_lock)

        // 设置全屏（在setContentView之后调用）
        setupFullscreen()
        supportActionBar?.hide()

        // 提取Intent数据
        extractIntentData()

        initViews()
        setupClickListeners()
        setupObservers()
        setupBackPressedHandler()

        // 启动HTTP轮询机制
//        loginViewModel.startHttpPolling()

        // 立即生成并显示二维码
        generateAndShowQRCode()

        // 根据入口类型决定启动流程
        when (entryType) {
            LockActivityEntryType.THIRD_PARTY_APP -> {
                // 通用入口，启动2分钟倒计时后退出Activity
                startGeneralEntryFlow()
            }

            LockActivityEntryType.COUNTDOWN_ENDED -> {
                // 倒计时结束，启动3分钟倒计时后自动跳转到清理页面
                startCountdownEndedFlow()
            }
        }
    }

    private fun setupFullscreen() {
        try {
            window.insetsController?.let { controller ->
                controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                controller.systemBarsBehavior =
                    WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            } ?: run {
                // 如果 insetsController 为 null，使用传统方法
                @Suppress("DEPRECATION")
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN
                )
            }
        } catch (e: Exception) {
            // 如果设置全屏失败，记录错误但不崩溃
            e.printStackTrace()
        }
    }

    /**
     * 提取Intent数据
     */
    private fun extractIntentData() {
        targetPackageName = intent.getStringExtra(EXTRA_TARGET_PACKAGE_NAME)
        targetClassName = intent.getStringExtra(EXTRA_TARGET_CLASS_NAME)
        targetAppName = intent.getStringExtra(EXTRA_TARGET_APP_NAME)
        previousPackageName = intent.getStringExtra(EXTRA_PREVIOUS_PACKAGE_NAME)
        showLoginUI = intent.getBooleanExtra(EXTRA_SHOW_LOGIN_UI, false)

        // 获取入口类型
        val entryTypeString = intent.getStringExtra(EXTRA_ENTRY_TYPE)
        entryType = try {
            LockActivityEntryType.valueOf(entryTypeString ?: "THIRD_PARTY_APP")
        } catch (e: IllegalArgumentException) {
            LockActivityEntryType.THIRD_PARTY_APP
        }

        XLog.d("LockActivity启动参数 - 包名: $targetPackageName, 类名: $targetClassName, 应用名: $targetAppName, 前一个应用: $previousPackageName, 显示登录UI: $showLoginUI, 入口类型: $entryType")
    }

    private fun initViews() {

    }

    private fun setupClickListeners() {
        // 刷新登录状态
        binding.flQrcode.setOnClickListener {
            loginViewModel.refreshLoginStatus()
        }

        binding.btnBack.setOnClickListener {
            handleSmartNavigation()
        }
    }

    /**
     * 设置观察者
     */
    @SuppressLint("SetTextI18n")
    private fun setupObservers() {
        // 观察登录二维码Base64数据
        loginViewModel.qrCodeBase64.observe(this, Observer { base64 ->
            if (!base64.isNullOrEmpty()) {
                loadQRCodeFromBase64(base64)
            }
        })

        // 观察跳转到用户信息页面的事件
        loginViewModel.navigateToUserInfo.observe(this, Observer { userData ->
            if (userData != null) {
                XLog.d("登录成功，跳转到用户信息页面")
                navigateToUserInfoActivity()
                loginViewModel.clearNavigateToUserInfo()
            }
        })

        // 观察WebSocket状态 - 暂时注释，后续改为HTTP长轮询
        // loginViewModel.webSocketState.observe(this, Observer { state ->
        //     handleWebSocketStateChange(state)
        // })

        // 观察HTTP轮询状态
        loginViewModel.pollingActive.observe(this, Observer { isActive ->
            XLog.d("HTTP轮询状态变化: $isActive")
        })

        // 观察倒计时
        loginViewModel.countdownSeconds.observe(this, Observer { seconds ->
            binding.tvCountdownSeconds.text = "$seconds"
        })

        // 观察倒计时结束
        loginViewModel.countdownFinished.observe(this, Observer { finished ->
            if (finished) {
                when (entryType) {
                    LockActivityEntryType.COUNTDOWN_ENDED -> {
                        startAppDataCleanupActivity()
                    }

                    LockActivityEntryType.THIRD_PARTY_APP -> {
                        exitLockActivity()
                    }
                }
            }
        })
    }

    /**
     * 从Base64数据加载小程序码图片
     */
    private fun loadQRCodeFromBase64(base64: String) {
        try {
            // 解码Base64数据
            val imageBytes = Base64.decode(base64, Base64.DEFAULT)

            // 使用Coil加载图片
            binding.ivQrCode.load(imageBytes) {
                crossfade(true)
                placeholder(R.drawable.ic_launcher_foreground)
                error(R.drawable.ic_launcher_foreground)
            }

            XLog.d("加载小程序码成功，数据长度: ${imageBytes.size}")
        } catch (e: Exception) {
            XLog.e("加载小程序码失败", e)
        }
    }

    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 在登录模式下，返回键使用智能导航逻辑
                if (showLoginUI) {
                    loginViewModel.cancelLogin()
                    handleSmartNavigation()
                } else {
                    // 在锁定模式下禁用返回键
                }
            }
        })
    }


    /**
     * 智能导航处理
     * 根据入口类型决定返回逻辑：只有倒计时结束入口才重启应用，其他情况退到后台
     */
    private fun handleSmartNavigation() {
        try {
            XLog.d("handleSmartNavigation调用 - entryType: $entryType")

            // 只有通过倒计时结束进入的LockActivity才重启应用
            if (entryType == LockActivityEntryType.COUNTDOWN_ENDED) {
                XLog.d("倒计时结束入口，重启应用")
                navigateToHomePackage()
            } else {
                // 其他入口（如AIDL调用、第三方应用拦截等）退到后台，避免触发EmptyActivity启动
                XLog.d("非倒计时入口（${entryType}），退到后台")
                moveTaskToBack(true)
            }
        } catch (e: Exception) {
            XLog.e("智能导航处理失败，直接finish", e)
            finish()
        }
    }

    /**
     * 导航到homePackage（重启应用）
     */
    private fun navigateToHomePackage() {
        try {
            XLog.d("navigateToHomePackage调用 - entryType: $entryType")
            XLog.d(
                "调用栈: ${
                    Thread.currentThread().stackTrace.take(5).joinToString { it.toString() }
                }"
            )
//            WhitelistManager.openHomeApp()
//            finish()
            AdminManager.restartApp()
        } catch (e: Exception) {
            XLog.e("导航到homePackage失败", e)
            finish()
        }
    }

    /**
     * 启动倒计时结束流程
     */
    private fun startCountdownEndedFlow() {
        try {
            XLog.d("启动倒计时结束流程，开始3分钟倒计时")

            // 启动3分钟倒计时
            loginViewModel.startThreeMinuteCountdown()

        } catch (e: Exception) {
            XLog.e("启动倒计时结束流程失败", e)
        }
    }

    /**
     * 启动通用入口流程
     */
    private fun startGeneralEntryFlow() {
        try {
            XLog.d("启动通用入口流程，开始2分钟倒计时")

            // 启动2分钟倒计时
            loginViewModel.startTwoMinuteCountdown()

        } catch (e: Exception) {
            XLog.e("启动通用入口流程失败", e)
        }
    }

    /**
     * 退出LockActivity
     */
    private fun exitLockActivity() {
        try {
            XLog.d("2分钟倒计时结束，退出LockActivity - entryType: $entryType")

            // 停止HTTP轮询
            loginViewModel.stopHttpPolling()

            // 使用智能导航逻辑，保持与手动退出的一致性
            handleSmartNavigation()

        } catch (e: Exception) {
            XLog.e("退出LockActivity失败", e)
            // 异常情况下直接finish
            finish()
        }
    }

    /**
     * 跳转到用户信息页面
     */
    private fun navigateToUserInfoActivity() {
        try {
            XLog.d("启动用户信息页面")

            // 启动用户信息页面
            UserInfoActivity.start(this)

            // 退出当前页面
            finish()

        } catch (e: Exception) {
            XLog.e("跳转到用户信息页面失败", e)
            // 如果跳转失败，直接退出
            exitLockActivity()
        }
    }

    override fun onPause() {
        super.onPause()
        try {
            XLog.d("LockActivity onPause - 暂停倒计时和轮询")
            // 暂停倒计时
            loginViewModel.pauseCountdown()
            // 暂停HTTP轮询
            loginViewModel.pauseHttpPolling()
        } catch (e: Exception) {
            XLog.e("LockActivity暂停时处理失败", e)
        }
    }

    override fun onResume() {
        super.onResume()
        try {
            XLog.d("LockActivity onResume - 重新开始倒计时和轮询")
            // 重新生成并显示二维码
            generateAndShowQRCode()
            // 恢复HTTP轮询
            loginViewModel.resumeHttpPolling()
            // 恢复倒计时 - 重新开始
            loginViewModel.resumeCountdown(entryType.name)
        } catch (e: Exception) {
            XLog.e("LockActivity恢复时处理失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            // 停止HTTP轮询
            loginViewModel.stopHttpPolling()
            // 取消倒计时
            loginViewModel.cancelCountdown()
            // 注销广播接收器
        } catch (e: Exception) {
            XLog.e("LockActivity销毁时清理资源失败", e)
        }
    }

    /**
     * 启动应用数据清理Activity
     */
    private fun startAppDataCleanupActivity() {
        try {
            XLog.d("启动应用数据清理Activity")

            val intent = Intent(this, AppDataCleanupActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            startActivity(intent)

            // 结束当前Activity
            finish()

        } catch (e: Exception) {
            XLog.e("启动应用数据清理Activity失败", e)
        }
    }

    /**
     * 生成并显示二维码
     */
    private fun generateAndShowQRCode() {
        try {
            XLog.d("开始生成二维码")
            // 直接调用LoginViewModel生成二维码
            loginViewModel.generateQRCodeDirectly()
        } catch (e: Exception) {
            XLog.e("生成二维码失败", e)
        }
    }
}
