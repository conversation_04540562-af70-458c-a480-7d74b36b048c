package com.hwb.timecontroller.utils

import com.elvishew.xlog.XLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * 安全执行工具类
 * 提供统一的错误处理和日志记录
 * 
 * Author: huangwubin
 * Date: 2025/7/26
 */
object SafeExecutor {

    /**
     * 安全执行代码块，自动处理异常和日志
     * @param operation 操作名称，用于日志记录
     * @param onError 错误处理回调（可选）
     * @param block 要执行的代码块
     * @return 执行结果，失败时返回null
     */
    inline fun <T> execute(
        operation: String,
        noinline onError: ((Exception) -> Unit)? = null,
        block: () -> T
    ): T? {
        return try {
            block()
        } catch (e: Exception) {
            XLog.e("$operation 失败", e)
            onError?.invoke(e)
            null
        }
    }

    /**
     * 安全执行代码块，返回布尔值结果
     * @param operation 操作名称，用于日志记录
     * @param onError 错误处理回调（可选）
     * @param block 要执行的代码块
     * @return 执行结果，失败时返回false
     */
    inline fun executeBoolean(
        operation: String,
        noinline onError: ((Exception) -> Unit)? = null,
        block: () -> Boolean
    ): Boolean {
        return try {
            block()
        } catch (e: Exception) {
            XLog.e("$operation 失败", e)
            onError?.invoke(e)
            false
        }
    }

    /**
     * 安全执行代码块，无返回值
     * @param operation 操作名称，用于日志记录
     * @param onError 错误处理回调（可选）
     * @param block 要执行的代码块
     */
    inline fun executeVoid(
        operation: String,
        noinline onError: ((Exception) -> Unit)? = null,
        block: () -> Unit
    ) {
        try {
            block()
        } catch (e: Exception) {
            XLog.e("$operation 失败", e)
            onError?.invoke(e)
        }
    }

    /**
     * 安全执行协程代码块
     * @param scope 协程作用域
     * @param operation 操作名称，用于日志记录
     * @param onError 错误处理回调（可选）
     * @param block 要执行的协程代码块
     */
    inline fun executeCoroutine(
        scope: CoroutineScope,
        operation: String,
        noinline onError: ((Exception) -> Unit)? = null,
        noinline block: suspend () -> Unit
    ) {
        scope.launch {
            try {
                block()
            } catch (e: Exception) {
                XLog.e("$operation 失败", e)
                onError?.invoke(e)
            }
        }
    }

    /**
     * 安全执行代码块，带默认值
     * @param operation 操作名称，用于日志记录
     * @param defaultValue 失败时的默认返回值
     * @param onError 错误处理回调（可选）
     * @param block 要执行的代码块
     * @return 执行结果，失败时返回默认值
     */
    inline fun <T> executeWithDefault(
        operation: String,
        defaultValue: T,
        noinline onError: ((Exception) -> Unit)? = null,
        block: () -> T
    ): T {
        return try {
            block()
        } catch (e: Exception) {
            XLog.e("$operation 失败", e)
            onError?.invoke(e)
            defaultValue
        }
    }

    /**
     * 安全执行代码块，支持重试
     * @param operation 操作名称，用于日志记录
     * @param maxRetries 最大重试次数
     * @param retryDelay 重试间隔（毫秒）
     * @param onError 错误处理回调（可选）
     * @param block 要执行的代码块
     * @return 执行结果，失败时返回null
     */
    inline fun <T> executeWithRetry(
        operation: String,
        maxRetries: Int = 3,
        retryDelay: Long = 1000,
        noinline onError: ((Exception, Int) -> Unit)? = null,
        block: () -> T
    ): T? {
        repeat(maxRetries + 1) { attempt ->
            try {
                return block()
            } catch (e: Exception) {
                if (attempt < maxRetries) {
                    XLog.w("$operation 失败，第 ${attempt + 1} 次重试", e)
                    onError?.invoke(e, attempt + 1)
                    Thread.sleep(retryDelay)
                } else {
                    XLog.e("$operation 失败，已达最大重试次数", e)
                    onError?.invoke(e, attempt + 1)
                }
            }
        }
        return null
    }
}
