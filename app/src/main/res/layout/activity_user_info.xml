<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background_gradient"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- 返回按钮（隐藏） -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/lock_text_primary"
            android:visibility="gone" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="用户信息"
            android:textColor="@color/lock_text_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- 占位空间，保持标题居中 -->
        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 用户信息卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- 用户头像 -->
                <FrameLayout
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:id="@+id/iv_user_avatar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/circle_background"
                        android:contentDescription="用户头像"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_default_avatar" />

                    <!-- 头像加载指示器（可选） -->
                    <ProgressBar
                        android:id="@+id/pb_avatar_loading"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:indeterminateTint="@color/lock_accent_gold"
                        android:visibility="gone" />

                </FrameLayout>

                <!-- 用户昵称 -->
                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="用户昵称"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <!-- 用户余额 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/balance_background"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/ic_money"
                        android:tint="@color/lock_accent_gold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="余额："
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/tv_user_balance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/lock_accent_gold"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" 元"
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp" />

                    <!-- 刷新按钮 -->
                    <ImageView
                        android:id="@+id/iv_refresh_balance"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="刷新余额"
                        android:padding="4dp"
                        android:src="@drawable/ic_refresh"
                        android:tint="@color/lock_accent_gold" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_weight="1">

                <FrameLayout
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:layout_gravity="center"
                    android:background="@drawable/qr_card_background"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/iv_qr_code"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitCenter" />

                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="5dp"
                    android:text="扫码充值后请手动刷新余额"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />
            </LinearLayout>
        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 余额不足提示卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_balance_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/warning_background"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:src="@drawable/ic_warning"
                android:tint="@color/warning_text" />

            <TextView
                android:id="@+id/tv_balance_warning"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="余额不足，无法继续使用"
                android:textColor="@color/warning_text"
                android:textSize="14sp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 5分钟倒计时卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_countdown"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:visibility="visible"
        app:cardBackgroundColor="@color/countdown_background"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="系统将在以下时间后自动退出登录"
                android:textColor="@color/countdown_text"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_countdown_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="05:00"
                android:textColor="@color/countdown_time"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 占位空间 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- 开始游玩按钮 -->
    <Button
        android:id="@+id/btn_start_playing"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/modern_button_primary"
        android:text="开始租借平板"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- 退出登录按钮 -->
    <Button
        android:id="@+id/btn_logout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/logout_button_background"
        android:text="退出登录"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

</LinearLayout>
